const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');

// Game constants
const FRICTION = 0.98;
const BALL_RADIUS = 15;
const CUE_BALL_COLOR = '#FFFFFF';
const BALL_COLORS = ['#FF0000', '#FFFF00', '#0000FF'];

// Ball class
class Ball {
    constructor(x, y, color) {
        this.x = x;
        this.y = y;
        this.vx = 0;
        this.vy = 0;
        this.radius = BALL_RADIUS;
        this.color = color;
    }

    draw() {
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
        ctx.fillStyle = this.color;
        ctx.fill();
        ctx.closePath();
    }

    update() {
        // Apply friction
        this.vx *= FRICTION;
        this.vy *= FRICTION;
        
        // Stop if velocity is very small
        if (Math.abs(this.vx) < 0.1) this.vx = 0;
        if (Math.abs(this.vy) < 0.1) this.vy = 0;
        
        // Update position
        this.x += this.vx;
        this.y += this.vy;
        
        // Bounce off walls
        if (this.x - this.radius < 0 || this.x + this.radius > canvas.width) {
            this.vx = -this.vx;
        }
        if (this.y - this.radius < 0 || this.y + this.radius > canvas.height) {
            this.vy = -this.vy;
        }
    }
}

// Create balls
const cueBall = new Ball(200, canvas.height/2, CUE_BALL_COLOR);
const balls = [
    cueBall,
    new Ball(500, canvas.height/2 - 30, BALL_COLORS[0]),
    new Ball(500, canvas.height/2, BALL_COLORS[1]),
    new Ball(500, canvas.height/2 + 30, BALL_COLORS[2])
];

// Handle mouse events
let isDragging = false;
let startX, startY;

canvas.addEventListener('mousedown', (e) => {
    const rect = canvas.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    // Check if click is on cue ball
    const dx = mouseX - cueBall.x;
    const dy = mouseY - cueBall.y;
    if (dx*dx + dy*dy < cueBall.radius*cueBall.radius) {
        isDragging = true;
        startX = mouseX;
        startY = mouseY;
    }
});

canvas.addEventListener('mousemove', (e) => {
    if (!isDragging) return;
    
    const rect = canvas.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    // Draw aiming line
    draw();
    ctx.beginPath();
    ctx.moveTo(cueBall.x, cueBall.y);
    ctx.lineTo(cueBall.x - (mouseX - cueBall.x), cueBall.y - (mouseY - cueBall.y));
    ctx.strokeStyle = 'white';
    ctx.stroke();
});

canvas.addEventListener('mouseup', (e) => {
    if (!isDragging) return;
    
    const rect = canvas.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    // Calculate force based on drag distance
    const forceX = (startX - mouseX) * 0.1;
    const forceY = (startY - mouseY) * 0.1;
    
    // Apply force to cue ball
    cueBall.vx = forceX;
    cueBall.vy = forceY;
    
    isDragging = false;
});

// Check collision between two balls
function checkCollision(ball1, ball2) {
    const dx = ball2.x - ball1.x;
    const dy = ball2.y - ball1.y;
    const distance = Math.sqrt(dx*dx + dy*dy);
    
    if (distance < ball1.radius + ball2.radius) {
        // Calculate collision angle
        const angle = Math.atan2(dy, dx);
        
        // Calculate velocities after collision
        const speed1 = Math.sqrt(ball1.vx*ball1.vx + ball1.vy*ball1.vy);
        const speed2 = Math.sqrt(ball2.vx*ball2.vx + ball2.vy*ball2.vy);
        
        const direction1 = Math.atan2(ball1.vy, ball1.vx);
        const direction2 = Math.atan2(ball2.vy, ball2.vx);
        
        const velocityX1 = speed1 * Math.cos(direction1 - angle);
        const velocityY1 = speed1 * Math.sin(direction1 - angle);
        const velocityX2 = speed2 * Math.cos(direction2 - angle);
        const velocityY2 = speed2 * Math.sin(direction2 - angle);
        
        // Exchange velocities
        const finalVelocityX1 = velocityX2;
        const finalVelocityX2 = velocityX1;
        
        // Update velocities
        ball1.vx = Math.cos(angle) * finalVelocityX1 + Math.cos(angle + Math.PI/2) * velocityY1;
        ball1.vy = Math.sin(angle) * finalVelocityX1 + Math.sin(angle + Math.PI/2) * velocityY1;
        ball2.vx = Math.cos(angle) * finalVelocityX2 + Math.cos(angle + Math.PI/2) * velocityY2;
        ball2.vy = Math.sin(angle) * finalVelocityX2 + Math.sin(angle + Math.PI/2) * velocityY2;
        
        // Prevent balls from sticking together
        const overlap = (ball1.radius + ball2.radius) - distance;
        ball1.x -= overlap * Math.cos(angle) / 2;
        ball1.y -= overlap * Math.sin(angle) / 2;
        ball2.x += overlap * Math.cos(angle) / 2;
        ball2.y += overlap * Math.sin(angle) / 2;
    }
}

// Game loop
function draw() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw table pockets
    ctx.fillStyle = '#000';
    const pocketRadius = BALL_RADIUS * 1.5;
    const pockets = [
        {x: 0, y: 0},
        {x: canvas.width/2, y: 0},
        {x: canvas.width, y: 0},
        {x: 0, y: canvas.height},
        {x: canvas.width/2, y: canvas.height},
        {x: canvas.width, y: canvas.height}
    ];
    
    pockets.forEach(pocket => {
        ctx.beginPath();
        ctx.arc(pocket.x, pocket.y, pocketRadius, 0, Math.PI * 2);
        ctx.fill();
    });
    
    // Draw and update balls
    balls.forEach(ball => {
        ball.draw();
        ball.update();
    });
    
    // Check for collisions between balls
    for (let i = 0; i < balls.length; i++) {
        for (let j = i + 1; j < balls.length; j++) {
            checkCollision(balls[i], balls[j]);
        }
    }
    
    // Check if balls fall into pockets
    for (let i = balls.length - 1; i >= 0; i--) {
        const ball = balls[i];
        for (const pocket of pockets) {
            const dx = ball.x - pocket.x;
            const dy = ball.y - pocket.y;
            const distance = Math.sqrt(dx*dx + dy*dy);
            
            if (distance < pocketRadius) {
                // Don't remove cue ball, just reset it
                if (ball === cueBall) {
                    ball.x = 200;
                    ball.y = canvas.height/2;
                    ball.vx = 0;
                    ball.vy = 0;
                } else {
                    balls.splice(i, 1);
                }
                break;
            }
        }
    }
    
    requestAnimationFrame(draw);
}

draw();